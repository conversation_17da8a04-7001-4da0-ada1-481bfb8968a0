# USAGE

## 在线文档

本项目 CI 会自动 基于 [gnostic](https://github.com/MoeGolibrary/gnostic) 将 proto 转换为 open api 格式,
同时使用 [redoc](https://github.com/Redocly/redoc) 渲染成在线文档.

可以通过访问 <https://api-docs.t2.moego.pet/> 阅读, 这里也同时会渲染 restful api 的文档 (在顶部菜单选择即可).

**注意: 在线文档亦支持多分支, 即如果本仓库有 `feature-xxx` 分支, 则可以通过 <https://feature-xxx-grey-api-docs.t2.moego.pet> 访问该分支对应的接口文档**

**已知问题**:

1. 枚举在转换过程中, 会被转换成字符串枚举, 而我们的网关转换时会转换成数字
2. service 缺少 domain 层级的分组

**TODO**:

-   [ ] 支持查看分支接口变更

## Web 端使用

### 一般使用流程

1. 安装类型库:

    ```shell
    yarn add @moego/api-web@latest
    ```

2. 创建一个客户端 (注意这个客户端是全局的):

    ```typescript
    import { AccountInfoService } from '@moego/api-web/moego/api/account/v1/account_info_api';
    import { createHttpClient } from '@moego/api-web/createHttpClient';

    export const AccountInfoClient = createHttpClient<AccountInfoService>('moego.api.account.v1.AccountInfoService')(
        httpFetch,
    );
    ```

3. 调用客户端:

    ```typescript
    const { account } = await AccountInfoClient.getAccountInfo({});
    ```

### 分支开发

由于 npm 包管理对源码依赖支持很不友好, 所以这里发布到了 npm 中央仓库, 发布的机制是:

1. 如果是在 production 分支上, 则版本号基于 package.json 中的版本号并且: minor 版本 +1, patch 置 0, 使用 `latest` 作为 tag 发布
2. 如果是在开发分支上, 则版本号基于 package.json 中的版本号不变, 并且使用分支名 + BUILD_ID 作为后缀, 使用分支名作为 tag 发布

关于版本号标准和 npm 的 tag 机制, 请参考 <https://semver.org/> 和 <https://docs.npmjs.com/cli/v10/commands/npm-dist-tag>.

基于以上, 如果一个项目中, 涉及到接口变更, 则需要:

1. 先在本仓库中定义好接口并 push, 等待构建完成
2. 前端安装以该分支为 tag 的版本依赖:

    ```shell
    yarn add @moego/api-web@API_BRANCH_NAME # 这个是本仓库的分支名
    ```

3. 开发完成后, 首先要合并本仓库到主干, 并等待构建完成
4. 前端修改依赖回到主干版本:

    ```shell
    yarn add @moego/api-web@latest
    ```

5. 再合并前端及后端的 PR

**强烈建议各项目在 CI lint 环节, 禁止带有非 latest 分支依赖的 PR 合并.** 参考 [这里](https://github.com/MoeGolibrary/moego-svc-llm/blob/4c225437e78f138292c153149932eb1c6bf8d103/package.json#L15).

**已知问题**:

1. 目前 proto3 的 `optional` 关键字, 不可作用于数组 (`repeated`) 和 `map`, 所以转换工具只能统一将其视为 optional 或者非 optional,
   这样带来的问题是: 我们希望在返回的数据结构里面, 它是非 optional 的 (网关在转换的时候, 也会强制加上默认值 `[]/{}`), 但是在作为入参的时候
   又希望他是 optional 的. 这就矛盾了. 考虑到入参使用数组和 map 的场景较少, 所以均强制视为非 optional 的, 如果有入参使用 map 和 数组的
   情况, 需要自行转换处理一下, 或者使用 api-web 提供的 `OptionalRepeated<T>`.
2. required oneof 比较难表达, 目前 oneof 字段均为 optional.

**TODO**:

-   [ ] 保留 validate 相关扩展信息, 使前端可以直接用来做校验, 保证前后端校验规则一致.
