# SPECIFICATION

MoeGo API 定义.

-   [Quick start](#quick-start)
-   [Commands](#commands)
-   [Development flow](#development-flow)
-   [Specification](#specification)
    -   [Basic](#basic)
    -   [Domain](#domain)
    -   [Scopes](#scopes)
    -   [Service](#service)
    -   [Method](#method)
    -   [Models](#models)
    -   [Parameter](#parameter)
    -   [Response](#response)
    -   [基操](#基操)
-   [Deploy and access](#deploy-and-access)

## Quick start

1. Clone 本仓库
2. [安装并启动 Docker](https://docs.docker.com/get-docker/)
3. 在本仓库目录下执行命令: `make docker-install` (如果遇到网络问题,
   需要[配置 Docker 代理](https://docs.docker.com/desktop/settings/mac/#proxies))
4. 初始化 IDE (建议使用 [Goland](https://www.jetbrains.com/go/) 或者 [Idea](https://www.jetbrains.com/idea/) 作为开发工具):

    1. Goland 或者 Idea:

        1. 在菜单栏中依次点击: `Goland/Idea` -> `Preferences`
        2. 在弹窗中点击左侧菜单: `Language & Frameworks` -> `Protocol Buffers`
        3. 在配置中, 取消选中 `Configure automatically`
        4. 在 `Import paths` 中, 添加当前目录下的 `api` 和 `third_party` 两个目录, 最终配置如下:

            ![Configure screenshot](../assets/goland-configure.png)

    2. vscode: _待补充_

5. 在本仓库目录下执行命令 `yarn` 以初始化 git 相关工具.
6. 请参考 `todo` 及 `todo_user` 两个模块, 作为 demo 使用.
7. 开发流程和规则和其它 gradle 项目一致.
8. 创建新文件: 在命令行中执行 `make new` 根据提示输入 `domain`, `service` 以及选择要生成的文件类型即可.

## Commands

1. 本项目使用 `make` 管理所有 pb 相关动作, 可以通过 `make help` 查看所有支持的命令:

    ```bash
    Usage: make [target] ...

    Build steps:
      clean               clean up the cache & build output
      gen                 generate the language specific stubs
      format-gen          format the generated files
      pb                  generate the descriptor set

    Miscellaneous:
      help                Show this help

    Style:
      lint                lint the code style
      format              format the code style

    Tools:
      install             link buf modules to local, you should run this again after add new dependencies in buf.yaml

    Build:
      build               build the final outputs
    ```

2. 要运行以上命令, 需要安装比较多的 pb 相关依赖,
   参考 [protobuf-builder](https://github.com/MoeGolibrary/protobuf-builder/blob/production/Dockerfile), 为了简化了本地初始化难度,
   将这些依赖打包到了 `acrazing/protobuf-builder` 这个镜像里, 同时上述命令均支持在命令前增加 `docker-` 来在本地运行这个 docker 镜像执行, 比如 `make docker-install`
   表示通过 docker 将 buf.build 的依赖安装到 `third_party` 目录. 这也是为什么上面的 Quick start 为什么要求安装使用 docker 的原因.
3. 本地开发的过程中, 除了 `install` 命令需要执行一次外, 其它命令均不需要手动执行, git hooks 会帮助完成 format, lint 相关工作,
   CI 会帮助完成构建, 构建结果会保存在 [out](../out) 目录中.

## Development flow

在增加 API 设计环节后, 开发流程有一定的变化, 主要是前置接口设计, 以便于前后端流程并发, 整体参考如下.

1. 需求论证确认
2. 设计
3. 开发
    1. 系统设计
    2. **接口设计** _变化部分_
        1. 后端编写 api/svc 层接口, 提 PR, 请求前端/后端 Review
        2. 前端/后端 Review 接口
        3. 通过后合并到主干
    3. 业务逻辑实现 (前后端)
    4. 自测 (前后端)
    5. 联调 (前端)
    6. Review
4. 测试 (QA)
5. 发布
    1. 合并代码到主干
    2. 回归
    3. 发布

## Specification

### Basic

1. 所有接口定义源文件位于 [api](../api) 目录中
2. 针对不同语言的构建结果均位于 [out](../out) 目录中

### Domain

1. Domain 指域, 其格式为 `<slug>.<product>.<scope>.<domain_name>.<version>`, 其中:
    1. `<slug>` 为公司标识, 统一为 `moement`
    2. `<product>` 为产品线, 目前只有 `moego`
    3. `<scope>` 表示范围, [见下](#scopes)
    4. `<domain_name>` 为该域的名称
    5. `<version>` 为版本号, 格式为 `v<int>`
2. 域决定了 PB 的包名, 格式如 1 中所述
3. 域决定了 API 定义的文件目录: 与包名保持一致, 将 `.` 换成 `/`
4. 域中所有字段均为下划线小写格式
5. 目前只有 `moego` 这一个产品, 此产品忽略 `<slug>.` 部分

### Scopes

此处约定 [Domain](#domain) 中的 `scope` 的细节.

1. scope 是基于三层架构 (DAO, Service, UI) 来划定的, 但是其中 DAO 层未对外暴露接口, 同时 UI 层和 Service 层有大量返回数据结构需要复用,
   所以 scope 包含了三类: `models`, `service`, `ui`, 其中:
    1. `models` 中定义用于数据交换的实体, 只允许定义 `message` 和 `enum`, 可以在 `service` 和 `ui` 中被复用
    2. `service` 表示业务逻辑层, 可以定义服务及出入参
    3. `ui` 表示展示层, 可以定义服务及出入参, 根据使用场景不同, ui 层分为 `api`, `admin`, `open` 三类, 其中:
        1. `api` 表示由用户侧调用的接口
        2. `admin` 表示由后台 mis 系统调用的接口
        3. `open` 表示由开放平台调用的接口

### Service

1. Service 指域中的服务, 一个域中可能存在多个服务.
2. 服务名为**大驼峰**格式, 以 `Service` 结尾, 比如 `UserService`.
3. 一个服务占用一个 proto 文件, 文件名即为服务名, 比如 `UserService.proto`.

### Method

1. Method 指服务中的方法, 一个服务中可能存在多个方法
2. 方法名采用**大驼峰**格式
3. 方法名应该有明确的语义, 至少包含 `动作` 和 `作用对象`, 比如 `UserService` 中有效的方法名可以是 `GetUser`, `GetUserList`, `GetUserByEmail` 等, 但是 `Get`
   不是一个有效的方法名, 因为缺少作用对象
4. 方法的定义应该符合内聚原则:
    1. 简单情况下, 针对同一个实体的操作应该只允许在该实体对应的服务中提供方法, 比如 `User` 的方法应该由 `UserService` 提供, 而 `Business` 的方法应该由 `BusinessService`
       提供, 不可以反过来
    2. 避免向上耦合, 比如如果上述 `User` 和 `Business` 存在一对多绑定关系, 如果业务中存在 `GetUserListWithoutBusiness` 这种查询, 应该由 `BusinessService`
       而不是 `UserService` 提供.
5. 不要配置 `google.http` 选项.

### Models

1. 这里针对的是 `models` 这个 `scope` 下的数据结构的定义.
2. `models` 中可以定义 `message` (结构) 和 `enum` (值).
3. 数据实体对应的 `message` 以 `Model` 结尾, 比如 `UserModel`, `SessionModel`, 这个实体应该包含该实体的所有字段.
4. 针对同一实体在不同场景下需要展示的字段不同, 针对针对同一个实体定义不同的视图 (View), 格式为 + `XxxModelYyyView`, 比如 `UserModelPublicView`.
5. **视图只允许减少实体中的字段而不能增加**.
6. **实体只允许作为接口返回参数, 不允许作为接口请求参数**, 注: `models` 中定义的 `enum` 是可以用来请求参数中的.
7. **实体及其视图中的所有字段在均必须有值 (如果确实需要 `null` 的语义才标记为 `optional`)**, 如果存在只返回实体中的部分字段的情况, 请使用不同的视图来表达.
8. 如无必要, 不要增加视图.
9. 尽可能通过组合而非嵌套来表达实体关联.
10. models 中的文件允许有三类: `defs`, `enums`, `models`, 文件名格式分别为 `xxx_yyy_defs.proto`, `xxx_yyy_enums.proto`, `xxx_yyy_models.proto`, 其中:

    1. `enums` 中只允许定义枚举类型, 枚举可以被请求和返回共享使用
    2. `defs` 中定义请求与返回中共享的 message, 要求:
        1. 此类 message 格式为 `XxxDef`, 如 `AttachmentDef`
        2. 此类 message 可同时被请求和返回使用, 但是只允许作为嵌套结构使用, 不允许直接作为请求或返回
        3. 所有字段必须加上校验, 规则同 [Parameter](#parameter)
        4. 一个 `Def` 消息的语义必须是单一的, 和 `Model`/`View` 要求一样, 不允许随意添加 `optional` 来表达多义场景
    3. `models` 如上 3-9 描述

### Parameter

1. 此处讨论的是接口请求参数
2. 所有字段必须配置 `validate.rules` 选项, 用来约束参数要求, 具体参考 [protoc-gen-validate](https://github.com/bufbuild/protoc-gen-validate):
    1. 如果是 `string`, 必须限制 `max_len` (or `max_bytes`).
    2. 如果是 `enum`, 必须指定 `defined_only` 和 `not_in: [0]`.
3. 如果是可选参数, 必须标记为 `optional`, 否则表达必填.
4. 请求参数必须定义在使用它的服务文件中, 并且只允许被该服务使用, 不同服务之间不允许共享.
5. 请求参数字段含义必须准确, 尤其是是否为可选参数. 比如针对实体的创建接口和更新接口, 创建接口是没有 id 的, 更新接口有 id,
   同时, 如果更新接口实现了增量更新, 则更新接口中的所有字段 (除了 id) 均为 optional, 而添加接口则不是, 所以更新接口和创建接口所要求的参数不是同一个结构.
6. 请求参数不可直接或间接使用 `models` 中定义的实体.
7. 针对服务层:
    1. 如果数据存在所有权关系, 必须通过参数严格限定所有权.
8. 参数命名规范:

    |            | 请求参数   | 返回参数    | 说明                                                                                |
    | ---------- | ---------- | ----------- | ----------------------------------------------------------------------------------- |
    | ui 层      | XxxParams  | XxxResult   | 指所有 ui 层 scope, 如 api/client/open/admin 等, 所有参数/返回以 Params/Result 结尾 |
    | service 层 | XxxRequest | XxxResponse | 服务层所有参数/返回以 Request/Response 结尾                                         |

### Response

1. 此处讨论的是接口的返回参数.
2. 返回参数中的所有字段都表示为必填 (后端返回时必须填充有效的值).
3. 返回参数和请求参数一样, 不允许不同服务之间共享.
4. 返回参数可以直接或间接使用 `models` 中的实体, 但是在直接使用实体作为返回参数时要注意其对严谨性与可扩展性的约束.

### Field

1. 指 `message` 中的字段
2. 强制使用**下划线小写**格式
3. 表达逻辑状态使用 `bool` 类型, 并且使用 `is_xxx` 格式
4. 列表字段 (`repeated type xxxs`) 强制使用复数形式, 如 `repeated UserModel users`, 禁止使用 `xxx_list`, 以及单数形式, 如 `user`, `user_list`.

### 基操

1. 所有接口, 字段, 枚举值都要有注释
2. 禁止 `breaking change`, 具体参考 [Buf - Breaking Change Detection](https://docs.buf.build/breaking/overview).
    1. 如果有任何有效信息要删除, 请通过 option 标记为 `[deprecated = true]`

## Deploy and access

1. gRPC 类型的服务部署流程和其它 k8s 服务一致
2. 针对 service 层接口, 服务部署后, 直接通过 grpc 客户端访问即可
3. 针对 api 层接口, 服务部署后, 前端可以:
    1. 通过支持接口访问的域名直接访问
    2. 在 api 定义合并到主干并且网关 ([moego-gateway](../../moego-gateway)) 自动部署后, 通过标准 HTTP 协议访问 (只支持 unary api), 其中:
        1. 域名为支持 API 访问的域名, 比如 `api`, `go`, `booking`, `form` 等
        2. 方法为 `POST`
        3. 路径为 `<domain>.<Service>/<Method>`,
           参考 [envoy auto_mapping](https://www.envoyproxy.io/docs/envoy/latest/api-v3/extensions/filters/http/grpc_json_transcoder/v3/transcoder.proto#envoy-v3-api-field-extensions-filters-http-grpc-json-transcoder-v3-grpcjsontranscoder-auto-mapping)
        4. `Content-Type` 为 `application/json`
        5. 参数均在 body 中, 使用 `json` 序列化,
           参考 [Protobuf JSON Mapping](https://developers.google.com/protocol-buffers/docs/proto3#json).
