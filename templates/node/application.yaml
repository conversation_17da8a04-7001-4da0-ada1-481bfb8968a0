# 后端api-v3配置文件叫"application.yaml"。后端如果在这个文件下有新增服务，可以咨询相应功能开发的同事，添加服务配置
# 添加后，执行脚本: yarn sync-applications。命令会自动生成文件: templates/node/serviceClient.ts
# git add templates/node/serviceClient.ts templates/node/application.yaml
# git commit -m "feat: 新增服务配置"

moego:
  grpc:
    server:
      empty-server-enabled: true
      debug-enabled: true
      port: 9090
    client:
      stubs:
        - service: moego.service.metadata.**
          authority: moego-svc-metadata:9090
        - service: moego.service.todo.**
          authority: moego-svc-todo:9090
        - service: moego.service.order.v1.**
          authority: moego-svc-order:9090
        - service: moego.service.temp_order.**
          authority: moego-svc-order-v2:9090

        - service: moego.service.customer.**
          authority: moego-svc-customer:9090
        - service: moego.service.activity_log.**
          authority: moego-svc-activity-log:9090
        - service: moego.service.message.v1.**
          authority: moego-svc-message:9090
        - service: moego.service.message.v2.**
          authority: moego-svc-message-v2:9090
        - service: moego.service.auto_message.v1.**
          authority: moego-svc-auto-message:9090
        - service: moego.service.account.**
          authority: moego-svc-account:9090
        - service: moego.service.agreement.**
          authority: moego-svc-agreement:9090
        - service: moego.service.price_checker.**
          authority: moego-svc-price-checker:9090
        - service: moego.service.ai_assistant.v1.**
          authority: moego-svc-ai-assistant:9090
        - service: moego.service.risk_control.**
          authority: moego-svc-risk-control:9090
        - service: moego.service.google_partner.**
          authority: moego-svc-google-partner:9090
        - service: moego.service.marketing.**
          authority: moego-svc-marketing:9090

        - service: moego.service.organization.v1.*
          authority: moego-svc-organization:9090

        - service: moego.service.business_customer.v1.*
          authority: moego-svc-business-customer:9090
        - service: moego.service.permission.v1.*
          authority: moego-svc-permission:9090
        - service: moego.service.offering.v1.*
          authority: moego-svc-offering:9090
        - service: moego.service.appointment.v1.*
          authority: moego-svc-appointment:9090
        - service: moego.service.online_booking.v1.*
          authority: moego-svc-online-booking:9090
        - service: moego.service.reporting.v2.*
          authority: moego-svc-reporting-v2:9090

        - service: moego.service.membership.v1.*
          authority: moego-svc-membership:9090
        - service: moego.service.subscription.v1.*
          authority: moego-svc-subscription:9090

        - service: moego.service.capital.v1.*
          authority: moego-svc-capital:9090
        - service: moego.service.map.v1.*
          authority: moego-svc-map:9090

        - service: moego.service.user_profile.v1.*
          authority: moego-svc-user-profile:9090
        - service: moego.service.file.v2.*
          authority: moego-svc-file:9090
        - service: moego.service.branded_app.v1.*
          authority: moego-svc-branded-app:9090
        - service: moego.service.accounting.v1.*
          authority: moego-svc-accounting:9090

        - service: moego.service.finance_tools.v1.*
          authority: moego-svc-finance-tools:9090

        - service: moego.service.engagement.v1.*
          authority: moego-svc-engagement:9090

        - service: moego.service.smart_scheduler.v1.*
          authority: moego-svc-smart-scheduler:9090

        - service: moego.service.automation.v1.*
          authority: moego-svc-automation:9090

        - service: moego.service.payment.v2.*
          authority: moego-svc-payment:9090
        - service: moego.service.order.v2.**
          authority: moego-svc-order-v2:9090