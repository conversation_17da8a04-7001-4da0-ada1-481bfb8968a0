/*
 * @since 2023-10-09 13:15:01
 * <AUTHOR> <<EMAIL>>
 */

import {createHttpClient, OptionalRepeated} from "./createHttpClient";
import * as assert from "assert";

interface FooService {
    $fullName: 'root.FooService';

    helloWorld(input: boolean): Promise<any>;
}


async function testHello(url: string, payload: boolean, options: number) {
    return [url, payload, options]
}

interface BarMessage {
    id: number;
    value: string | undefined;
    arr1: number[];
    arr2: [number];
    optional?: string;
    map1: { [key: string]: number };
    map2: { [key: number]: number };
    map3: { [key: string | number]: number };
    map4: { [key in 'foo' | 1]: number };
    map5: { name: string }
}

type OptionalRepeatedBar = OptionalRepeated<BarMessage>;

if (require.main === module) {
    const client = createHttpClient<FooService>("root.FooService")(testHello);

    client.helloWorld(true, 1).then((r) => {
        assert.deepStrictEqual(r, ['/root.FooService/HelloWorld', true, 1]);
    })

    const bar: OptionalRepeatedBar = {
        arr2: [0],
        map4: {foo: 1, 1: 2},
        map5: {name: ""},
        value: "",
        id: 1
    }

    // @ts-expect-error
    const bar1: OptionalRepeatedBar = {
        arr2: [0],
        map5: {name: ""},
        value: "",
        id: 1
    }

    // @ts-expect-error
    const bar2: OptionalRepeatedBar = {
        arr2: [0],
        map4: {foo: 1, 1: 2},
        value: "",
        id: 1
    }

    // @ts-expect-error
    const bar3: OptionalRepeatedBar = {
        arr2: [0],
        map4: {foo: 1, 1: 2},
        map5: {name: ""},
        id: 1
    }

    // @ts-expect-error
    const bar4: OptionalRepeatedBar = {
        arr2: [0],
        map4: {foo: 1, 1: 2},
        map5: {name: ""},
        value: "",
    }
}
