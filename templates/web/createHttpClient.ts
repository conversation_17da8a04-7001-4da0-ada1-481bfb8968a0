/*
 * @since 2023-10-09 13:15:01
 * <AUTHOR> <<EMAIL>>
 */

export type RepeatedKeys<T> = {
    [P in keyof T]-?: any[] extends T[P] ? P : { [key: string]: any } extends T[P] ? P : {
        [key: number]: any
    } extends T[P] ? P : never;
}[keyof T]

export type OptionalRepeated<T> = Omit<T, RepeatedKeys<T>> & {
    [P in RepeatedKeys<T>]+?: T[P]
}

export interface ServiceDefinition<N extends string> {
    $fullName: N;
}

export type HttpClientDefinition<S extends ServiceDefinition<any>, A extends any[], AL = A['length']> = {
    [P in Exclude<keyof S, '$fullName'>]: AL extends 0 ? S[P] : S[P] extends (input: infer I) => Promise<infer O> ? (input: I, ...args: A) => Promise<O> : never;
}

/**
 * 根据一个 pb service 定义, 创建一个 http client.
 *
 * @example
 * ```typescript
 * import { AccountInfoService } from '@moego/api-web/moego/api/account/v1/account_info_api';
 *
 * const AccountInfoClient = createHttpClient<AccountInfoService>('moego.api.account.v1.AccountInfoService')(httpFetch);
 *
 * const { account } = await AccountInfoClient.getAccountInfo({});
 * ```
 *
 * 注意: 这个函数返回的是一个函数, 返回的这个函数接收 fetch 的实现, 所以通常需要链式调用两次,
 * 即 createHttpClient<XxxService>('xxx')(httpFetch).
 * 才能得到一个对象. 这么做是为了减少泛型书写复杂度.
 *
 * 注意:  第一层调用时, 要求显式传入 service 全名的原因是避免 value import, 目前编译的结果是只包含类型的, 避免引入过多无用 js 代码.
 *
 * 注意: 这个方法后续理论上会废弃掉, 原因是这个作用域是全局的, 后续的 http client 应该是基于 store 级别的作用域生成 (需要等待全局状态管理
 * 库构建完成才可用, 当然也可以在自己的项目里面自行实现).
 * @param service
 */
export function createHttpClient<S extends ServiceDefinition<any>>(service: S['$fullName']) {
    return <A extends any[]>(fetch: (url: string, input: any, ...args: A) => Promise<any>): HttpClientDefinition<S, A> => {
        return new Proxy<any>({}, {
            get(target: any, p: string | symbol, receiver: any): any {
                if (target[p] || typeof p !== 'string') {
                    return target[p]
                }
                const url = '/' + service + '/' + p.substring(0, 1).toUpperCase() + p.substring(1);
                target[p] = function (input: any, ...args: A) {
                    return fetch(url, input, ...args);
                }
                return target[p];
            }
        })
    }
}
