// @gencoder.generated: api/{{_replaceAll properties.protoModelsPackage '.' '/'}}/{{_snakeCase table.name}}_model.proto

syntax = "proto3";

package {{properties.protoModelsPackage}};

{{#if (_hasTimestamp table)}}
import "google/protobuf/timestamp.proto";
{{/if}}
{{#if (_hasDate table)}}
import "google/type/date.proto";
{{/if}}

option go_package = "{{properties.goModelsPackage}}";
option java_multiple_files = true;
option java_package = "{{properties.javaModelsPackage}}";

// table: {{table.name}}
// comment: {{table.comment}}
// indexes:
     {{#each table.indexes}}
//   {{name}}: ({{#each columns}}{{name}}{{#unless @last}}, {{/unless}}{{/each}})
     {{/each}}
message {{_pascalCase table.name}}Model {
  {{#each table.columns}}
  {{#if comment}}// {{comment}}{{else}}// {{_replaceAll (_snakeCase name) '_' ' '}}{{/if}}
  {{#if isNullable}}optional {{/if}}{{> 'proto_type_mapping.partial.hbs' columnType=type}} {{_snakeCase name}} = {{ordinal}};
  {{/each}}
}
