// @gencoder.generated: api/{{_replaceAll properties.protoServicePackage '.' '/'}}/{{_snakeCase table.name}}_service.proto

syntax = "proto3";

package {{properties.protoServicePackage}};

{{#if (_hasTimestamp table)}}
import "google/protobuf/timestamp.proto";
{{/if}}
{{#if (_hasDate table)}}
import "google/type/date.proto";
{{/if}}

option go_package = "{{properties.goServicePackage}}";
option java_multiple_files = true;
option java_package = "{{properties.javaServicePackage}}";

// Create{{_pascalCase table.name}} request
message Create{{_pascalCase table.name}}Request {
{{#each table.columns}}
  {{#if isPrimaryKey}}
  {{else}}
  {{#if comment}}// {{comment}}{{else}}// {{_replaceAll (_snakeCase name) '_' ' '}}{{/if}}
  optional {{> 'proto_type_mapping.partial.hbs' columnType=type}} {{_snakeCase name}} = {{ordinal}};
  {{/if}}
{{/each}}
}

// Update{{_pascalCase table.name}} request
message Update{{_pascalCase table.name}}Request {
{{#each table.columns}}
  {{#if isPrimaryKey}}
  {{#if comment}}// {{comment}}{{else}}// {{_replaceAll (_snakeCase name) '_' ' '}}{{/if}}
  {{> 'proto_type_mapping.partial.hbs' columnType=type}} {{_snakeCase name}} = {{ordinal}};
  {{else}}
  {{#if comment}}// {{comment}}{{else}}// {{_replaceAll (_snakeCase name) '_' ' '}}{{/if}}
  optional {{> 'proto_type_mapping.partial.hbs' columnType=type}} {{_snakeCase name}} = {{ordinal}};
  {{/if}}
{{/each}}
}
