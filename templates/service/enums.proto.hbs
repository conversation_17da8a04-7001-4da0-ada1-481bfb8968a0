// @since {{> date }}
// <AUTHOR> user }} <{{> email }}>

syntax = "proto3";

package moego.models.{{ domain }};

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/{{ domainPath domain }};{{ goPackage domain 'pb' }}";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.{{ domain }}";

// A demo enum, please delete me
enum {{ service }}Demo {
  // unspecified value
  {{ constantCase service }}_DEMO_UNSPECIFIED = 0;
}
