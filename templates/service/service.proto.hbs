// @since {{> date }}
// <AUTHOR> user }} <{{> email }}>

syntax = "proto3";

package moego.service.{{ domain }};

import "moego/models/{{ domainPath domain }}/{{ snakeCase service }}_defs.proto";
import "moego/models/{{ domainPath domain }}/{{ snakeCase service }}_models.proto";
import "moego/utils/v2/operation_messages.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/{{ domainPath domain }};{{ goPackage domain 'svcpb' }}";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.{{ domain }}";

// create {{ lowerCase (snakeCase service) }} request
message Create{{ service }}Request {
  // the {{ lowerCase (snakeCase service) }} def
  moego.models.{{ domain }}.{{ service }}CreateDef {{ snakeCase service }}_def = 1 [(validate.rules).message = { required: true }];

  // operation
  moego.utils.v2.OperationRequest operation = 15;
}

// create {{ lowerCase (snakeCase service) }} response
message Create{{ service }}Response {
  // the created {{ lowerCase (snakeCase service) }}
  moego.models.{{ domain }}.{{ service }}Model {{ lowerCase (snakeCase service) }} = 1;
}

// get {{ lowerCase (snakeCase service) }} request
message Get{{ service }}Request {
  // the id
  int64 id = 1 [(validate.rules).int64 = { gt: 0 }];
}

// get {{ lowerCase (snakeCase service) }} response
message Get{{ service }}Response {
  // the {{ lowerCase (snakeCase service) }}
  moego.models.{{ domain }}.{{ service }}Model {{ lowerCase (snakeCase service) }} = 1;
}

// list {{ lowerCase (snakeCase service) }} request
message List{{ service }}sRequest {
  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 15;
}

// list {{ lowerCase (snakeCase service) }} response
message List{{ service }}sResponse {
  // the {{ lowerCase (snakeCase service) }}
  repeated moego.models.{{ domain }}.{{ service }}Model {{ lowerCase (snakeCase service) }}s = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// update {{ lowerCase (snakeCase service) }} request
message Update{{ service }}Request {
  // id
  int64 id = 1;
  // the current revision, if set, will check the
  // revision is it or not, if no, will throw an error
  optional int32 revision = 2;
  // the {{ lowerCase (snakeCase service) }} def
  moego.models.{{ domain }}.{{ service }}UpdateDef {{ snakeCase service }}_def = 3 [(validate.rules).message = { required: true }];

  // operation
  moego.utils.v2.OperationRequest operation = 15;
}

// update {{ lowerCase (snakeCase service) }} response
message Update{{ service }}Response {
  // the updated {{ lowerCase (snakeCase service) }}
  moego.models.{{ domain }}.{{ service }}Model {{ lowerCase (snakeCase service) }} = 1;
}

// delete {{ lowerCase (snakeCase service) }} request
message Delete{{ service }}Request {
  // the id
  int64 id = 1 [(validate.rules).int64 = { gt: 0 }];

  // operation
  moego.utils.v2.OperationRequest operation = 15;
}

// delete {{ lowerCase (snakeCase service) }} response
message Delete{{ service }}Response {
}

// the {{ lowerCase (snakeCase service) }} service
service {{ service }}Service {
  // create {{ lowerCase (snakeCase service) }}
  rpc Create{{ service }}(Create{{ service }}Request) returns (Create{{ service }}Response);
  // get {{ lowerCase (snakeCase service) }}
  rpc Get{{ service }}(Get{{ service }}Request) returns (Get{{ service }}Response);
  // list {{ lowerCase (snakeCase service) }}
  rpc List{{ service }}s(List{{ service }}sRequest) returns (List{{ service }}sResponse);
  // update {{ lowerCase (snakeCase service) }}
  rpc Update{{ service }}(Update{{ service }}Request) returns (Update{{ service }}Response);
  // delete {{ lowerCase (snakeCase service) }}
  rpc Delete{{ service }}(Delete{{ service }}Request) returns (Delete{{ service }}Response);
}
