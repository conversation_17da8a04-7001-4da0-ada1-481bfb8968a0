// @since {{> date }}
// <AUTHOR> user }} <{{> email }}>

syntax = "proto3";

package moego.models.{{ domain }};

import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/{{ domainPath domain }};{{ goPackage domain 'pb' }}";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.{{ domain }}";

// The {{ service }} model
message {{ service }}Model {
  // the unique id
  int64 id = 1;

  // the current revision, default is 1, any update with +1
  int32 revision = 12;

  // the create time
  google.protobuf.Timestamp created_at = 13;
  // the update time
  optional google.protobuf.Timestamp updated_at = 14;
  // the delete time, non-null means is deleted
  optional google.protobuf.Timestamp deleted_at = 15;
}
