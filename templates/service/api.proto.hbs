// @since {{> date }}
// <AUTHOR> user }} <{{> email }}>

syntax = "proto3";

package moego.api.{{ domain }};

import "moego/models/{{ domainPath domain }}/{{ snakeCase service }}_defs.proto";
import "moego/models/{{ domainPath domain }}/{{ snakeCase service }}_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/{{ domainPath domain }};{{ goPackage domain 'apipb' }}";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.{{ domain }}";

// create {{ lowerCase (snakeCase service) }} params
message Create{{ service }}Params {
  // the {{ lowerCase (snakeCase service) }} def
  moego.models.{{ domain }}.{{ service }}CreateDef {{ snakeCase service }}_def = 1 [(validate.rules).message = { required: true }];
}

// create {{ lowerCase (snakeCase service) }} result
message Create{{ service }}Result {
  // the created {{ lowerCase (snakeCase service) }}
  moego.models.{{ domain }}.{{ service }}Model {{ lowerCase (snakeCase service) }} = 1;
}

// get {{ lowerCase (snakeCase service) }} params
message Get{{ service }}Params {
  // the id
  int64 id = 1 [(validate.rules).int64 = { gt: 0 }];
}

// get {{ lowerCase (snakeCase service) }} result
message Get{{ service }}Result {
  // the {{ lowerCase (snakeCase service) }}
  moego.models.{{ domain }}.{{ service }}Model {{ lowerCase (snakeCase service) }} = 1;
}

// list {{ lowerCase (snakeCase service) }} params
message List{{ service }}sParams {
  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 15;
}

// list {{ lowerCase (snakeCase service) }} result
message List{{ service }}sResult {
  // the {{ lowerCase (snakeCase service) }}
  repeated moego.models.{{ domain }}.{{ service }}Model {{ lowerCase (snakeCase service) }}s = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 15;
}

// create {{ lowerCase (snakeCase service) }} params
message Update{{ service }}Params {
  // id
  int64 id = 1;
  // the current revision, if set, will check the
  // revision is it or not, if no, will throw an error
  optional int32 revision = 2;
  // the {{ lowerCase (snakeCase service) }} def
  moego.models.{{ domain }}.{{ service }}UpdateDef {{ snakeCase service }}_def = 3 [(validate.rules).message = { required: true }];
}

// create {{ lowerCase (snakeCase service) }} result
message Update{{ service }}Result {
  // the updated {{ lowerCase (snakeCase service) }}
  moego.models.{{ domain }}.{{ service }}Model {{ lowerCase (snakeCase service) }} = 1;
}

// get {{ lowerCase (snakeCase service) }} params
message Delete{{ service }}Params {
  // the id
  int64 id = 1 [(validate.rules).int64 = { gt: 0 }];
}

// get {{ lowerCase (snakeCase service) }} result
message Delete{{ service }}Result {
}

// the {{ lowerCase (snakeCase service) }} service
service {{ service }}Service {
  // create {{ lowerCase (snakeCase service) }}
  rpc Create{{ service }}(Create{{ service }}Params) returns (Create{{ service }}Result);
  // get {{ lowerCase (snakeCase service) }}
  rpc Get{{ service }}(Get{{ service }}Params) returns (Get{{ service }}Result);
  // list {{ lowerCase (snakeCase service) }}
  rpc List{{ service }}s(List{{ service }}sParams) returns (List{{ service }}sResult);
  // update {{ lowerCase (snakeCase service) }}
  rpc Update{{ service }}(Update{{ service }}Params) returns (Update{{ service }}Result);
  // delete {{ lowerCase (snakeCase service) }}
  rpc Delete{{ service }}(Delete{{ service }}Params) returns (Delete{{ service }}Result);
}
