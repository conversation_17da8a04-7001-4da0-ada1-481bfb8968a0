name: "MoeGo API Docs"

on:
  push:
    branches:
      - '**'
  workflow_dispatch:

jobs:
  lint:
    runs-on: ubuntu-latest
    env:
      AWS_REGION: "${{ vars.AWS_REGION }}"
      AWS_ECR_URL: "${{ vars.AWS_ECR_URL }}"
    outputs:
      START_TIME: ${{ steps.init.outputs.START_TIME }}
      COMMITER_EMAIL: ${{ steps.init.outputs.COMMITER_EMAIL }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 1
      - name: Fetch branch main
        id: init
        shell: bash
        run: |
          MOEGO_EMAIL=$(git log -1 --pretty=format:'%an/%ae' | awk -F'/' '{
              name=$1; email=$2;
              if (email ~ /@moego\.pet$/) { print email }
              else { split(name, n, " "); print tolower(n[1]) "@moego.pet" }
          }')
          echo "START_TIME=$(date +%s)" >> $GITHUB_OUTPUT
          echo "COMMITER_EMAIL=${MOEGO_EMAIL}" >> $GITHUB_OUTPUT

          git fetch origin main --depth=1
          git branch -a
      - name: Setup node
        uses: actions/setup-node@v4
        with:
          # See: https://github.com/MoeGolibrary/moego-dockerfiles/blob/2ee97a5187317a75636b50c4fa20b06a048a2edd/src/Dockerfile.protobuf-builder#L13C20-L13C27
          node-version: 16.19.1
      - uses: bufbuild/buf-action@v1
        with:
          version: 1.55.1
          setup_only: true
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ${{ env.AWS_REGION }}
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      - name: Login AWS ECR
        run: |
          aws --region ${AWS_REGION} ecr get-login-password | docker login -u AWS --password-stdin ${AWS_ECR_URL}
      - name: Lock files
        run: |
          EXISTING_SUM=$(cat lock.sum || true)
          EXPECTED_SUM=$(sha1sum yarn.lock)

          if [[ "$EXISTING_SUM" == "$EXPECTED_SUM" ]]; then
            echo "Lock sum is fine, keep workspace content."
          else
            echo "Lock sum mismatch, will clean workspace."
            echo "Existing SUM: ${EXISTING_SUM}"
            echo "Current SUM: ${EXPECTED_SUM}"
            rm -f lock.sum
            git clean -xfd
          fi
      - name: lint api docs
        env:
          IS_CI: true
        run: |
          ./ci/lint.sh

  build:
    runs-on: ubuntu-latest
    env:
      BUILD_ID: "${{ github.run_id }}"
      RUN_ATTEMPT: "${{ github.run_attempt }}"
      BRANCH_NAME: "${{ github.ref_name }}"
      AWS_REGION: "${{ vars.AWS_REGION }}"
      AWS_ECR_URL: "${{ vars.AWS_ECR_URL }}"
      NPM_PUBLISHER_USR: "${{ vars.NPM_PUBLISHER_USR }}"
      NPM_PUBLISHER_PSW: "${{ secrets.NPM_PUBLISHER_PSW }}"
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 1
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ${{ env.AWS_REGION }}
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      - name: Login AWS ECR
        run: |
          aws --region ${AWS_REGION} ecr get-login-password | docker login -u AWS --password-stdin ${AWS_ECR_URL}
      - name: build api docs
        run: |
          docker run --rm -v $(pwd):/app              \
            -e IS_CI=true                             \
            -e BUILD_ID=${BUILD_ID}                   \
            -e RUN_ATTEMPT=${{ github.run_attempt }}  \
            -e BRANCH_NAME=${BRANCH_NAME}             \
            -e NPM_PUBLISHER_USR=${NPM_PUBLISHER_USR} \
            -e NPM_PUBLISHER_PSW=${NPM_PUBLISHER_PSW} \
            -w /app                                   \
            693727988157.dkr.ecr.us-west-2.amazonaws.com/protobuf-builder:production ./ci/build.sh
      - name: build image
        run: |
          IMAGE_NAME=moego-api-docs:${BRANCH_NAME}
          REMOTE_IMAGE_NAME=${AWS_ECR_URL}/${IMAGE_NAME}
          docker build -t ${IMAGE_NAME} -f ci/Dockerfile .
          docker tag ${IMAGE_NAME} ${REMOTE_IMAGE_NAME}
          docker push ${REMOTE_IMAGE_NAME}
      - name: commit build output
        run: |
          git config core.hooksPath /
          git config user.name moego-devops
          git config user.email <EMAIL>
          git remote set-url origin https://github.com/${{ github.repository }}.git
          git add -A
          if ! git diff --staged --quiet; then
            git commit -m "ci: build api-docs with branch ${BRANCH_NAME} TECH-0"
            git push -u origin ${BRANCH_NAME}
          fi

  deploy:
    runs-on: ubuntu-latest
    needs: [build]
    env:
      BUILD_ID: "${{ github.ref_name }}-${{ github.run_id }}-${{ github.run_attempt }}"
      BRANCH_NAME: "${{ github.ref_name }}"
      AWS_REGION: "${{ vars.AWS_REGION }}"
      AWS_ECR_URL: "${{ vars.AWS_ECR_URL }}"
      GITHUB_IDENTITY: "yoooyle:${{ secrets.ADMIN_TOKEN_GITHUB }}"
    steps:
      - name: Init Git Identity
        shell: bash
        run: |
          git config --global user.name "MoeGo CI Bot"
          git config --global user.email "<EMAIL>"
          git config --global --add url."https://${GITHUB_IDENTITY}@github.com/MoeGolibrary/".insteadOf "https://github.com/MoeGolibrary/"
          git config --global --add url."https://${GITHUB_IDENTITY}@github.com/MoeGolibrary/".insteadOf "**************:MoeGolibrary/"
          git config --global --get-regexp 'url\.*'
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: ${{ env.AWS_REGION }}
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      - name: Login AWS ECR
        run: |
          aws --region ${AWS_REGION} ecr get-login-password | docker login -u AWS --password-stdin ${AWS_ECR_URL}
      - name: Setup Kubernetes
        run: |
          aws eks --region ${AWS_REGION} update-kubeconfig --name moego-server-cluster-development --alias moego-server-cluster-development
      - uses: clowdhaus/argo-cd-action/@main
        env:
          GITHUB_TOKEN: ${{ secrets.ADMIN_TOKEN_GITHUB }}
        with:
          command: version
          options: --client
      - name: Set ArgoCD
        shell: bash
        run: |
          argocd login argocd.devops.moego.pet:443 --username ${{ secrets.ARGOCD_CI_USERNAME }} --password ${{ secrets.ARGOCD_CI_PASSWORD }} --grpc-web
          argocd version
      - name: deploy api docs
        shell: bash
        run: |
          WORK_DIR=repos
          REPO_NAME=$(basename ${{ github.repository }})

          mkdir ${WORK_DIR}
          cd ${WORK_DIR}
          git clone https://github.com/MoeGolibrary/moego-k8s-apps.git
          cd moego-k8s-apps
          chmod +x ./scripts/*.sh

          ENV=testing
          IMAGE_TAG=${BRANCH_NAME}
          COMMIT_SHA=${{ github.sha }}

          bash ./scripts/deploy-services.sh \
              --repo=${REPO_NAME} \
              --env=${ENV} \
              --build-id=${BUILD_ID} \
              --service-branch-image-canary-pairs="${REPO_NAME}:${BRANCH_NAME},${IMAGE_TAG},true,${COMMIT_SHA}"

          bash ./scripts/sync-services.sh \
              --env=${ENV} \
              --service-branch-pairs="${REPO_NAME}:${BRANCH_NAME}"

  notify:
    runs-on: ubuntu-latest
    needs: [lint, build, deploy]
    if: always()
    env:
      START_TIME: ${{ needs.lint.outputs.START_TIME }}
      MOEGO_EMAIL: ${{ needs.lint.outputs.COMMITER_EMAIL }}
    steps:
      - name: push notification
        shell: bash
        run: |
          END_TIME=$(date +%s)
          DURATION=$((END_TIME - START_TIME))
          MINUTES=$((DURATION / 60))
          SECONDS=$((DURATION % 60))

          if [ "$MINUTES" -gt 0 ]; then
            MSG_DURATION="${MINUTES}m${SECONDS}s"
          else
            MSG_DURATION="${SECONDS}s"
          fi

          if [[ "${{ needs.lint.result }}" == "failure" || "${{ needs.build.result }}" == "failure" || "${{ needs.deploy.result }}" == "failure" ]]; then
            MSG_STATUS="FAILURE"
          else
            MSG_STATUS="SUCCESS"
          fi

          MSG_URL="https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}"
          # MSG_EMOJI=${{ job.status == 'success' && ':white_check_mark:' || ':x:' }}
          MSG_REPO=$(basename ${{ github.repository }})
          MSG_BRANCH=${{ github.ref_name }}
          MSG_RUN_ID=${{ github.run_id }}
          NOTIFY_MSG="<${MSG_URL}|*BUILD ${MSG_STATUS} (${MSG_DURATION}): ${MSG_REPO} » ${MSG_BRANCH} (run #${MSG_RUN_ID})*>"
          NOTIFY_URL=${{ vars.JOB_NOTIFY_URL }}
          BODY="{\"email\":\"${MOEGO_EMAIL}\",\"message\":\"${NOTIFY_MSG}\"}"

          echo "Email: ${MOEGO_EMAIL}, Duration: ${MSG_DURATION}, Notify: ${NOTIFY_MSG}"
          curl -X POST -H 'Content-type: application/json' --data "${BODY}" ${NOTIFY_URL}
