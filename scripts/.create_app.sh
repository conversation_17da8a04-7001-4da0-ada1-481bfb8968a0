#!/bin/bash

# Purpose: 通过脚本创建新服务
# 目前完成的功能：
# 1. 根据pb模板生成proto文件
# 2. 根据pb模板生成错误码段
# 3. 根据pb模板生成服务

# 未来(或许)完成的功能：
# 1. CI/CD 相关

# <AUTHOR> <EMAIL>

source scripts/common.sh

BACKEND_APP_DIR='backend/app'
BACKEND_PROTO_DIR='backend/proto'
branchName=`git rev-parse --abbrev-ref HEAD`

echo -e "${YELLOW}current branch: ${branchName}${NC}"
if [ "${branchName}" == "production" ]; then
  echo -e "${RED}forbidden to create service on production branch${NC}";
  exit 0
fi

moduleName=$1
serviceName=$2
isForcedBlocking=$3

type="moego"
owner=$(git config user.name)
owner_email=$(git config user.email)
echo -e "${YELLOW}current user: ${owner}${NC}"
if [ -z "${moduleName}" ]; then
  echo -e "${RED}please add module name, eg: ./.create_app.sh 模块名 服务名${NC}";
  exit 0
fi

if [ -z "${serviceName}" ]; then
  echo -e "${RED}please add service name, eg: ./.create_app.sh 模块名 服务名${NC}";
  exit 0
elif [ -d "${BACKEND_APP_DIR}/${serviceName}" ]; then
  echo -e "${RED}service already exists, please switch to another service name${NC}";
  exit 0
elif [ -d "${BACKEND_PROTO_DIR}/${serviceName}" ]; then
  echo -e "${RED}service already exists, please check and try again${NC}";
  exit 0
fi 
moduleName=$(echo ${moduleName} | awk '{print tolower($0)}')
serviceName=$(echo ${serviceName} | awk '{print tolower($0)}')
newServiceName=$(echo "${serviceName}" | awk -F"_" '{for(i=1;i<=NF;i++){$i=toupper(substr($i,1,1)) substr($i,2)}} 1' | sed 's/ //g')
echo -e "newServiceName: ${newServiceName}, serviceName: ${serviceName}, moduleName: ${moduleName}"

# 根据pb 模板构建service 对应的proto文件
cp -r ${BACKEND_PROTO_DIR}/template/. ${BACKEND_PROTO_DIR}/${serviceName}
cd ${BACKEND_PROTO_DIR}/${serviceName}/v1
mv xxx_service.proto ${serviceName}_service.proto
sed -i '' -e "s/xxx/${serviceName}/g;s/template/${serviceName}/g" ${serviceName}_service.proto
goPackageName=$(echo "${serviceName}" | tr -d '_')
sed -i '' -e "s|;${serviceName}pb|;${goPackageName}pb|g" ${serviceName}_service.proto
sed -i '' -e "s/ReplaceMe/${newServiceName}Service/g" ${serviceName}_service.proto
cd - 
echo -e "${GREEN}create proto files done ${CHECK}${NC}"
######################################################### 生成错误码段
echo -e "${YELLOW}create error code...${NC}"
go run backend/tools/cmd/generateerrorcode/main.go --moduleYmlConfigPath=./backend/.modules.yml --moduleName=${moduleName} --serviceName=${serviceName}
if [ $? == 1 ]; then
  rm -r ${BACKEND_PROTO_DIR}/${serviceName}
  exit 0
fi
for line in `cat  test_dir/msec.txt`
do
    sed -i '' -e "s/00000001/${line}/g" ${BACKEND_PROTO_DIR}/${serviceName}/v1/${serviceName}_service.proto
done
moduleOwner=""
for line in `cat  test_dir/mowner.txt`
do
  moduleOwner=${line}
done
if [ "${owner}" != "${moduleOwner}" ]; then
    owner=${owner},${moduleOwner}
fi
rm -r test_dir
echo -e "${GREEN}create error code done ${CHECK}${NC}"
######################################################### 生成服务
echo -e "${YELLOW}create service files...${NC}"
cp -r template/template-go/. ${BACKEND_APP_DIR}/${serviceName}
cd ${BACKEND_APP_DIR}/${serviceName}

# 处理 main.go
sed -i '' -e "s|template\/template-go|${BACKEND_APP_DIR}\/${serviceName}|g" main.go
sed -i '' -e "s/helloworld/${serviceName}/g" main.go
sed -i '' -e "s/HelloworldService_ServiceDesc/${newServiceName}Service_ServiceDesc/g" main.go
sed -i '' -e "s/NewGreeter/New${newServiceName}/g" main.go
sed -i '' -e "s/\/\/1//g" main.go

# 处理 xxx_service.go
mv service/greeter_service.go service/${serviceName}_service.go
sed -i '' -e "s/Greeter/${newServiceName}/g" service/${serviceName}_service.go
sed -i '' -e "s/helloworld/${serviceName}/g" service/${serviceName}_service.go
sed -i '' -e "s/UnimplementedHelloworldServiceServer/Unimplemented${newServiceName}ServiceServer/g" service/${serviceName}_service.go

# 处理 utils.go
sed -i '' -e "s/utils/${goPackageName}utils/g" utils/utils.go

# 处理 metadata.yaml
dashedServiceName=${serviceName//_/-}
sed -i '' -e "s|\${metadata.name}|moego-${dashedServiceName}|g" metadata.yaml
sed -i '' -e "s|\${metadata.codeowners}|${owner_email}|g" metadata.yaml

# 处理 config.yaml
find config -name "*.yaml" | xargs sed -i '' -e "s/helloworld.v1.Greeter/${serviceName}.v1.${newServiceName}Service/g"

# 处理 go import 路径
find . -name "*.go" | xargs sed -i '' -e "s|template\/template-go|${BACKEND_APP_DIR}\/${serviceName}|g"
find . -name "*.go" | xargs sed -i '' -e "s/${serviceName}pb/${goPackageName}pb/g"

cd -
${BAZEL_COMMAND} run //:gazelle -- ${BACKEND_APP_DIR}/${serviceName} ${BACKEND_PROTO_DIR}/${serviceName}

# 自动生成CODEOWNERS文件
echo -e "${YELLOW}update CODEOWNERS file...${NC}"
./scripts/generate_codeowners.sh


echo -e "${YELLOW}Please modify ${BACKEND_APP_DIR}/${serviceName}/config.yaml and ${BACKEND_APP_DIR}/${serviceName}/README.md${NC}"
echo -e "${YELLOW}If you need to generate proto files, please run 'make proto'${NC}"
echo -e "${GREEN}create service ${serviceName} done ${STAR}${NC}"
