// @since 2024-06-24 18:09:23
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.models.appointment.v1;

import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "moego/models/appointment/v1/daily_report_enums.proto";
import "moego/models/offering/v1/service_enum.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1;appointmentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.appointment.v1";

// report def
message ReportDef {
  // daily report content
  ContentDef content = 1 [(validate.rules).message = {required: true}];
}

// content
message ContentDef {
  // photo
  repeated string photos = 1 [(validate.rules).repeated = {
    max_items: 5
    items: {
      string: {
        uri: true
        max_len: 1000
      }
    }
  }];
  // video
  repeated string videos = 2 [(validate.rules).repeated = {
    max_items: 1
    items: {
      string: {
        uri: true
        max_len: 1000
      }
    }
  }];
  // overall feedbacks
  repeated QuestionDef feedbacks = 3 [(validate.rules).repeated = {min_items: 1}];
  // theme color
  string theme_color = 4 [(validate.rules).string = {pattern: "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$"}];
  // light theme color
  optional string light_theme_color = 5 [(validate.rules).string = {pattern: "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$"}];
}

// question
message QuestionDef {
  // category
  QuestionCategoryType category = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: 0
  }];
  // type, 1: single choice, 2: multiple choice, 3: input text, 4: short input text, 5: tag choice
  QuestionType type = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: 0
  }];
  // key, unique for each question
  string key = 4 [(validate.rules).string = {
    min_len: 1
    max_len: 100
    pattern: "^[a-zA-Z0-9_]+$"
  }];
  // title, question title
  string title = 5 [(validate.rules).string = {max_len: 100}];
  // required
  bool required = 6;
  // show
  bool show = 7;
  // options, only for single choice and multiple choice
  repeated string options = 8 [(validate.rules).repeated = {
    items: {
      string: {max_len: 1000}
    }
  }];
  // choices, only for single choice and multiple choice
  repeated string choices = 9 [(validate.rules).repeated = {
    items: {
      string: {max_len: 1000}
    }
  }];
  // custom options
  repeated string custom_options = 10 [(validate.rules).repeated = {
    items: {
      string: {max_len: 1000}
    }
  }];
  // input text, only for input text
  string input_text = 11 [(validate.rules).string = {max_len: 100000}];
  // placeholder, input text placeholder
  string placeholder = 12 [(validate.rules).string = {max_len: 300}];
}

// daily report sent history def
message SentHistoryRecordDef {
  // report
  ReportDef report = 1;
  // description
  string description = 2;
  // service date
  google.type.Date service_date = 3;
  // send time
  google.protobuf.Timestamp send_time = 4;
  // sent success
  bool sent_success = 5;
}

// sent result def
message SentResultDef {
  // appointment id
  int64 appointment_id = 1;
  // pet id
  int64 pet_id = 2;
  // sent success
  bool sent_success = 3;
  // error message
  string error_message = 4;
}

// daily report config def
message DailyReportConfigDef {
  // the id
  int64 id = 1;
  // customer id
  int64 customer_id = 2;
  // appointment id
  int64 appointment_id = 3;
  // pet id
  int64 pet_id = 4;
  // uuid
  string uuid = 5;
  // report
  models.appointment.v1.ReportDef report = 6;
  // report card status
  moego.models.appointment.v1.ReportCardStatus status = 7;
  // update time
  google.protobuf.Timestamp update_time = 8;
  // send time
  google.protobuf.Timestamp send_time = 9;
  // send method
  moego.models.appointment.v1.SendMethod send_method = 10;
  // service date
  google.type.Date service_date = 11;
}

// daily report config def for migration
message DailyReportConfigMigrateDef {
  // the id
  int64 id = 1;
  // business id
  int64 business_id = 2;
  // company id
  int64 company_id = 3;
  // customer id
  int64 customer_id = 4;
  // appointment id
  int64 appointment_id = 5;
  // pet id
  int64 pet_id = 6;
  // uuid
  string uuid = 7;
  // template json
  string template_json = 8;
  // update by
  int64 update_by = 9;
  // create time
  google.protobuf.Timestamp create_time = 10;
  // update time
  google.protobuf.Timestamp update_time = 11;
  // service date
  google.type.Date service_date = 12;
  // status
  string status = 13;
}

// daily report send log def for migration
message DailyReportSendLogMigrateDef {
  // id
  int64 id = 1;
  // daily report id
  int64 daily_report_id = 2;
  // content json
  string content_json = 3;
  // description
  string description = 4;
  // service date
  google.type.Date service_date = 5;
  // send time
  google.protobuf.Timestamp send_time = 6;
  // update by
  int64 update_by = 7;
  // create time
  google.protobuf.Timestamp create_time = 8;
  // update time
  google.protobuf.Timestamp update_time = 9;
  // error message
  string error_message = 10;
  // send status
  bool sent_success = 11;
  // send method
  SendMethod send_method = 12;
}

// list daily report config filter
message ListDailyReportConfigFilter {
  // report card status
  optional moego.models.appointment.v1.ReportCardStatus status = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: 0
  }];
  // service item types
  repeated moego.models.offering.v1.ServiceItemType service_item_types = 2 [(validate.rules).repeated = {
    unique: true
    items: {
      enum: {
        defined_only: true
        not_in: [0]
      }
    }
  }];
  // start date
  optional google.type.Date start_date = 3;
  // end date
  optional google.type.Date end_date = 4;
  // pet id
  optional int64 pet_id = 5 [(validate.rules).int64 = {gt: 0}];
  // daily report ids
  repeated int64 daily_report_ids = 6 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}
