// @since 2024-06-24 10:14:35
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.appointment.v1;

import "google/type/date.proto";
import "moego/models/appointment/v1/daily_report_defs.proto";
import "moego/models/appointment/v1/daily_report_enums.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/appointment/v1;appointmentsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.appointment.v1";

// get daily report request
message GetDailyReportConfigRequest {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
  // pet id
  int64 pet_id = 2 [(validate.rules).int64 = {gt: 0}];
  // service date
  google.type.Date service_date = 3 [(validate.rules).message = {required: true}];
  // company id
  int64 company_id = 4 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 5 [(validate.rules).int64 = {gt: 0}];
}

// get daily report response
message GetDailyReportConfigResponse {
  // the id
  int64 id = 1;
  // report
  models.appointment.v1.ReportDef report = 2;
  // report card status
  moego.models.appointment.v1.ReportCardStatus status = 3;
}

// list daily report request
message ListDailyReportConfigRequest {
  // appointment ids
  repeated int64 appointment_ids = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1000
    items: {
      int64: {gt: 0}
    }
  }];
  // service dates
  repeated google.type.Date service_date = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1000
  }];
  // company id
  int64 company_id = 4 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 5 [(validate.rules).int64 = {gt: 0}];
}

// list daily report response
message ListDailyReportConfigResponse {
  // report list
  repeated models.appointment.v1.DailyReportConfigDef report_configs = 1;
}

// get daily report config request
message GetDailyReportSentResultRequest {
  // appointment ids
  repeated int64 appointment_ids = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1000
    items: {
      int64: {gt: 0}
    }
  }];
  // service date
  google.type.Date service_date = 2 [(validate.rules).message = {required: true}];
  // company id
  int64 company_id = 4 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 5 [(validate.rules).int64 = {gt: 0}];
}

// get daily report config response
message GetDailyReportSentResultResponse {
  // sent results
  repeated models.appointment.v1.SentResultDef sent_results = 1;
}

// create daily report request
message UpsertDailyReportConfigRequest {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
  // pet id
  int64 pet_id = 2 [(validate.rules).int64 = {gt: 0}];
  // customer id
  int64 customer_id = 3 [(validate.rules).int64 = {gt: 0}];
  // service date
  google.type.Date service_date = 4 [(validate.rules).message = {required: true}];
  // report
  models.appointment.v1.ReportDef report = 5 [(validate.rules).message = {required: true}];
  // company id
  int64 company_id = 6 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 7 [(validate.rules).int64 = {gt: 0}];
  // staff id
  int64 staff_id = 8 [(validate.rules).int64 = {gt: 0}];
}

// get daily report response
message UpsertDailyReportConfigResponse {
  // the id
  int64 id = 1;
  // uuid
  string uuid = 2;
}

// get daily report request
message GetDailyReportSentHistoryRequest {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
  // pet id
  int64 pet_id = 2 [(validate.rules).int64 = {gt: 0}];
  // company id
  int64 company_id = 3 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 4 [(validate.rules).int64 = {gt: 0}];
}

// get daily report response
message GetDailyReportSentHistoryResponse {
  // sent history list
  repeated models.appointment.v1.SentHistoryRecordDef sent_history_records = 1;
}

// get daily report request
message GetDailyReportForCustomerRequest {
  // uuid
  string uuid = 1 [(validate.rules).string = {max_len: 50}];
}

// get daily report response
message GetDailyReportForCustomerResponse {
  // report
  models.appointment.v1.ReportDef report = 1;
  // service date
  google.type.Date service_date = 2;
  // pet id
  int64 pet_id = 3;
  // business id
  int64 business_id = 4;
  // company id
  int64 company_id = 5;
}

// get daily report request
message GenerateMessageContentRequest {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
  // pet id
  int64 pet_id = 2 [(validate.rules).int64 = {gt: 0}];
  // service date
  google.type.Date service_date = 3 [(validate.rules).message = {required: true}];
  // company id
  int64 company_id = 4 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 5 [(validate.rules).int64 = {gt: 0}];
}

// get daily report response
message GenerateMessageContentResponse {
  // message for sending
  string message = 1;
}

// send message request
message SendMessageRequest {
  // id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // company id
  int64 company_id = 3 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 4 [(validate.rules).int64 = {gt: 0}];
  // staff id
  int64 staff_id = 5 [(validate.rules).int64 = {gt: 0}];
  // send method (default is SMS)
  optional moego.models.appointment.v1.SendMethod send_method = 6 [(validate.rules).enum = {
    defined_only: true
    not_in: 0
  }];
  // recipient emails
  repeated string recipient_emails = 7 [(validate.rules).repeated = {
    unique: true
    items: {
      string: {
        email: true
        min_len: 1
      }
    }
  }];
}

// send message response
message SendMessageResponse {
  // result
  bool result = 1;
}

// list daily report config by filter request
message ListDailyReportConfigByFilterRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // list daily report config def
  models.appointment.v1.ListDailyReportConfigFilter filter = 3 [(validate.rules).message = {required: true}];
  // pagination
  moego.utils.v2.PaginationRequest pagination = 4 [(validate.rules).message = {required: true}];
}

// list daily report config by filter response
message ListDailyReportConfigByFilterResponse {
  // report list
  repeated models.appointment.v1.DailyReportConfigDef report_configs = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// batch send daily draft report request
message BatchSendDailyDraftReportRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // daily report ids
  repeated int64 daily_report_ids = 3 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
  // send method
  moego.models.appointment.v1.SendMethod send_method = 4 [(validate.rules).enum = {
    defined_only: true
    not_in: 0
  }];
  // staff id
  int64 staff_id = 5 [(validate.rules).int64 = {gt: 0}];
}

// batch send daily draft report response
message BatchSendDailyDraftReportResponse {}

// batch delete daily report config request
message BatchDeleteDailyReportConfigRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // daily report ids
  repeated int64 daily_report_ids = 3 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// batch delete daily report config response
message BatchDeleteDailyReportConfigResponse {}

// generate sms content by ids
message GenerateMessageContentByIdRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
  // daily report ids
  repeated int64 daily_report_ids = 3 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// get daily report response
message GenerateMessageContentByIdResponse {
  // messages for sending
  repeated DailyReportMessage daily_report_messages = 1;

  // daily report message
  message DailyReportMessage {
    // id
    int64 id = 1;
    // generate message
    string message = 2;
  }
}

// list daily report config for migrate request
message ListDailyReportConfigForMigrateRequest {
  // business id
  optional int64 business_id = 1;
  // pagination
  moego.utils.v2.PaginationRequest pagination = 2 [(validate.rules).message = {required: true}];
}

// list daily report config for migrate response
message ListDailyReportConfigForMigrateResponse {
  // report list
  repeated models.appointment.v1.DailyReportConfigMigrateDef report_configs = 1;
}

// list daily report send log for migrate request
message ListDailyReportSendLogForMigrateRequest {
  // pagination
  moego.utils.v2.PaginationRequest pagination = 1 [(validate.rules).message = {required: true}];
}

// list daily report send log for migrate response
message ListDailyReportSendLogForMigrateResponse {
  // report list
  repeated models.appointment.v1.DailyReportSendLogMigrateDef report_send_logs = 1;
}

// list daily report config by ids request
message ListDailyReportConfigByIdsRequest {
  // daily report ids
  repeated int64 daily_report_ids = 1 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// list daily report config by ids response
message ListDailyReportConfigByIdsResponse {
  // report list
  repeated models.appointment.v1.DailyReportConfigMigrateDef report_configs = 1;
}

// the daily report service
service DailyReportService {
  // get daily report config
  rpc GetDailyReportConfig(GetDailyReportConfigRequest) returns (GetDailyReportConfigResponse);
  // list daily report config
  rpc ListDailyReportConfig(ListDailyReportConfigRequest) returns (ListDailyReportConfigResponse);
  // get daily report config sent result
  rpc GetDailyReportSentResult(GetDailyReportSentResultRequest) returns (GetDailyReportSentResultResponse);
  // upsert daily report config
  rpc UpsertDailyReportConfig(UpsertDailyReportConfigRequest) returns (UpsertDailyReportConfigResponse);
  // get daily report sent history
  rpc GetDailyReportSentHistory(GetDailyReportSentHistoryRequest) returns (GetDailyReportSentHistoryResponse);
  // get daily report for customer
  rpc GetDailyReportForCustomer(GetDailyReportForCustomerRequest) returns (GetDailyReportForCustomerResponse);
  // generate message content
  rpc GenerateMessageContent(GenerateMessageContentRequest) returns (GenerateMessageContentResponse);
  // send message
  rpc SendMessage(SendMessageRequest) returns (SendMessageResponse);
  // list daily report config by filter
  rpc ListDailyReportConfigByFilter(ListDailyReportConfigByFilterRequest) returns (ListDailyReportConfigByFilterResponse);
  // batch send daily draft report
  rpc BatchSendDailyDraftReport(BatchSendDailyDraftReportRequest) returns (BatchSendDailyDraftReportResponse);
  // batch delete daily report config
  rpc BatchDeleteDailyReportConfig(BatchDeleteDailyReportConfigRequest) returns (BatchDeleteDailyReportConfigResponse);
  // GenerateMessageContentById
  rpc GenerateMessageContentById(GenerateMessageContentByIdRequest) returns (GenerateMessageContentByIdResponse);
  // ListDailyReportConfigForMigrate
  rpc ListDailyReportConfigForMigrate(ListDailyReportConfigForMigrateRequest) returns (ListDailyReportConfigForMigrateResponse);
  // ListDailyReportSendLogForMigrate
  rpc ListDailyReportSendLogForMigrate(ListDailyReportSendLogForMigrateRequest) returns (ListDailyReportSendLogForMigrateResponse);
  // ListDailyReportConfigByIds
  rpc ListDailyReportConfigByIds(ListDailyReportConfigByIdsRequest) returns (ListDailyReportConfigByIdsResponse);
}
