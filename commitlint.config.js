/*
 * @since 2021-01-11 18:10:33
 * <AUTHOR> <<EMAIL>>
 */

module.exports = {
  extends: ['@commitlint/config-angular'],
  rules: {
    'references-empty': [2, 'never'],
    'header-max-length': [2, 'always', 100],
  },
  ignores: [(commit) => commit.match(/^ci: commit change for commit .*/)],
  defaultIgnores: true,
  parserPreset: {
    parserOpts: {
      issuePrefixes: [
        'GROOM-',
        'OBV-',
        'APP-',
        'CS-',
        'MOE-',
        'TECH-',
        'ERP-',
        'FDN-',
        'IFRBE-',
        'MER-',
        'CA-',
        'FIN-',
        'CRM-',
        'EN-',
        'ENT-',
        'IFRFE-',
        'GRM-',
      ],
    },
  },
};
