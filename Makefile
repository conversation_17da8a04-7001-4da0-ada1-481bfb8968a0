SCRIPT_VERSION=v1.0
SCRIPT_AUTHOR=Junbao <<EMAIL>>

HELP_FUN = \
    %help; while(<>){push@{$$help{$$2//'options'}},[$$1,$$3] \
    if/^([\w-_]+)\s*:.*\#\#(?:@([^:]+):)?\s(.*)$$/}; \
    print"$$_:\n", map"  $$_->[0]".(" "x(20-length($$_->[0])))."$$_->[1]\n",\
    @{$$help{$$_}},"\n" for keys %help; \

help: 													##@Miscellaneous: Show this help
	@echo "Usage: make [target] ...\n"
	@perl -e '$(HELP_FUN)' $(MAKEFILE_LIST)
	@echo "Written by $(SCRIPT_AUTHOR), version $(SCRIPT_VERSION)"
	@echo "Please report any bug or error to the author."

.PHONY: lint format clean gen pb redoc format-gen build install

build: clean gen redoc 			##@Build: build the final outputs

format-lint: format lint				##@Style: run all the style steps

all: format-lint build 					##@Build: run all the build steps

lint: 													##@Style: lint the code style
	@buf lint api
	@buf breaking --against '.git#branch=origin/main,subdir=api' api
	@echo "Lint done!"

format_check:
	@buf format --exit-code -d api
	@echo "Format check passed!"

format:													##@Style: format the code style
	@buf format -w api
	@echo "Format done!"

clean:													##@Build steps: clean up the cache & build output
	@rm -rf temp out npm
	@mkdir -p temp out
	@echo "Clean up done!"

gen:														##@Build steps: generate the language specific stubs
	@buf generate --include-imports --timeout 5m api
	@rm -rf temp/java/com/google
	@find temp -path "**/hidden/**" -delete
	@mv temp/go/github.com/MoeGolibrary/moego-api-definitions/out/go out/go
	@mv temp/web out/web
	@mv temp/node out/node
	@echo "Generate language specific files done!"
gen-go:
	@buf generate --template buf.gen.go.yaml --include-imports --timeout 5m api
	@find temp -path "**/hidden/**" -delete
	@rm -rf out/go
	@mv temp/go/github.com/MoeGolibrary/moego-api-definitions/out/go out/go
	@echo "Generate language specific files done!"
format-gen:											##@Build steps: format the generated files
	@clang-format -i --sort-includes $$(find out -type f -name '*.cc' -o -name '*.h')
	@prettier --loglevel silent --write out
	@gofmt -s -w out
	@goimports -w out
	@echo "Format generated files done!"

pb:															##@Build steps: generate the descriptor set
	@buf build -o out/moego.bin --as-file-descriptor-set api
	@echo "Generate descriptor set done!"

PWD = $(shell pwd)
BUF_CACHE_DIR ?= $(HOME)/.cache/buf

install:												##@Tools: link buf modules to local, you should run this again after add new dependencies in buf.yaml
	@rm -rf third_party
	@mkdir -p third_party
	@yq -r '.deps[]' api/buf.yaml | xargs -I % buf export % -o third_party
	@echo "Generate deps directory done!"

SVC_FILE_LIST=$(shell find ./api/moego/service/ -type f -name '*.proto')
API_FILE_LIST=$(shell find ./api/moego/api/ -type f -name '*.proto')

redoc:
	@rm -rf out/redoc temp/redoc out/openapi.yaml
	@mkdir -p out/redoc temp/redoc
	@buf generate --template buf.gen.openapi.yaml --path 'api/moego/service' api
	@yq e -o=json '.' temp/redoc/openapi.yaml > out/redoc/svc.json
	@buf generate --template buf.gen.openapi.yaml --path 'api/moego/client' api
	@yq e -o=json '.' temp/redoc/openapi.yaml > out/redoc/client-api.json
	@buf generate --template buf.gen.openapi.yaml --path 'api/moego/api' api
	@yq e -o=json '.' temp/redoc/openapi.yaml > out/redoc/api.json
	@buf generate --template buf.gen.openapi.yaml --path 'api/moego/admin' api
	@yq e -o=json '.' temp/redoc/openapi.yaml > out/redoc/admin.json
	@buf generate --template buf.gen.openapi.yaml --path 'api/moego/enterprise' api
	@yq e -o=json '.' temp/redoc/openapi.yaml > out/redoc/enterprise-api.json
	@buf generate --template buf.gen.openapi.yaml api
	@mv temp/redoc/openapi.yaml out/openapi.yaml
	@cp templates/redoc.html out/redoc/index.html
	@node ci/transform_openapi.js
	@echo "Generate redoc done!"

new:													##@Tools: create new proto file
	@npx plop

docker-%:
	docker run --rm -v $(shell pwd):/data -w /data 693727988157.dkr.ecr.us-west-2.amazonaws.com/protobuf-builder:production make $*

health:
	@mkdir -p api/grpc/health/v1
	@curl -o api/grpc/health/v1/health.proto -sSL https://raw.githubusercontent.com/grpc/grpc/master/src/proto/grpc/health/v1/health.proto
