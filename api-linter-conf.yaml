# NOTE: 本文件仅供参考，梳理了需要普遍禁用的规则，目前还不能用于全项目的扫描。
- included_paths:
    - '**/*.proto'
  disabled_rules:
    - 'core::0158::response-next-page-token-field'
    - 'core::0132::response-unknown-fields'
    - 'core::0133::request-parent-required'
    - 'core::0158::request-page-token-field'
    - 'core::0133::method-signature'
    - 'core::0133::response-message-name'
    - 'core::0131::method-signature'
    - 'core::0133::request-id-field'
    - 'core::0132::request-unknown-fields'
    - 'core::0133::request-resource-field'
    - 'core::0191::java-outer-classname'
    - 'core::0131::request-unknown-fields'
    - 'core::0131::response-message-name'
    - 'core::0133::request-unknown-fields'
    - 'core::0191::java-package'
    - 'core::0131::request-name-required'
    - 'core::0127::http-annotation'
    - 'core::0158::request-page-size-field'
    - 'core::0132::request-field-types'
    - 'core::0132::request-parent-required'
    - 'core::0133::http-uri-parent'
    - 'core::0132::request-required-fields'
    - 'core::0131::request-required-fields'
    - 'core::0133::request-required-fields'
    - 'core::0140::uri'
    - 'core::0203::field-behavior-required'
    - 'core::0216::synonyms'
    - 'core::0123::resource-annotation'
