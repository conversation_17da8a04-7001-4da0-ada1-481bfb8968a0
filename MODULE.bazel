###############################################################################
# Bazel now uses Bzlmod by default to manage external dependencies.
# Please consider migrating your external dependencies from WORKSPACE to MODULE.bazel.
#
# For more details, please check https://github.com/bazelbuild/bazel/issues/18958
###############################################################################

"""Bazel module definition for moego project."""

module(
    name = "moego",
    version = "0.1",
)

bazel_dep(name = "bazel_features", version = "1.30.0")
bazel_dep(name = "bazel_skylib", version = "1.7.1")
bazel_dep(name = "rules_go", version = "0.54.1", repo_name = "io_bazel_rules_go")
bazel_dep(name = "rules_proto", version = "7.1.0")
bazel_dep(name = "rules_proto_grpc", version = "5.0.1")
bazel_dep(name = "gazelle", version = "0.43.0")
bazel_dep(name = "grpc", version = "1.70.1", repo_name = "com_github_grpc_grpc")
bazel_dep(name = "protobuf", version = "32.0-rc1", repo_name = "com_google_protobuf")
bazel_dep(name = "rules_license", version = "1.0.0")
bazel_dep(name = "rules_shell", version = "0.3.0")
bazel_dep(name = "rules_buf", version = "0.3.0")
bazel_dep(name = "protovalidate", version = "1.0.0-rc.6", repo_name = "com_github_bufbuild_protovalidate")
bazel_dep(name = "protoc-gen-validate", version = "1.2.1")
bazel_dep(name = "rules_android", version = "0.6.0")
bazel_dep(name = "googleapis", version = "0.0.0-20241220-5e258e33.bcr.1")
bazel_dep(name = "googleapis-go", version = "1.0.0")
bazel_dep(name = "rules_python", version = "1.4.1")

bazel_dep(name = "buildifier_prebuilt", version = "7.3.1", dev_dependency = True)

switched_rules = use_extension("@googleapis//:extensions.bzl", "switched_rules")
switched_rules.use_languages(
    go = True,
    grpc = True,
)

buf = use_extension("@rules_buf//buf:extensions.bzl", "buf")

# Override the default version of buf
# buf.toolchains(version = "v1.32.1")

go_sdk = use_extension("@io_bazel_rules_go//go:extensions.bzl", "go_sdk")
go_sdk.download(
    version = "1.23.9",
)
use_repo(
    go_sdk,
    "go_host_compatible_sdk_label",
    "go_toolchains",
    "io_bazel_rules_nogo",
)

register_toolchains("@io_bazel_rules_go//go:all")

# Python 工具链配置，解决 root 用户问题
python = use_extension("@rules_python//python/extensions:python.bzl", "python")
python.toolchain(
    ignore_root_user_error = True,
    is_default = True,
    python_version = "3.11",
)
use_repo(python, "python_versions")

go_deps = use_extension("@gazelle//:extensions.bzl", "go_deps")
go_deps.from_file(go_mod = "//:go.mod")
go_deps.gazelle_override(
    directives = [
        "gazelle:proto disable",
    ],
    path = "github.com/MoeGolibrary/moego-api-definitions",
)
go_deps.gazelle_override(
    directives = [
        "gazelle:proto disable",
    ],
    path = "github.com/DataDog/sketches-go",
)
go_deps.gazelle_override(
    build_file_generation = "on",
    path = "github.com/cncf/xds/go",
)
use_repo(
    go_deps,
    "build_buf_gen_go_bufbuild_protovalidate_protocolbuffers_go",
    "com_github_andygrunwald_go_jira",
    "com_github_aws_aws_msk_iam_sasl_signer_go",
    "com_github_aws_aws_sdk_go_v2",
    "com_github_aws_aws_sdk_go_v2_config",
    "com_github_aws_aws_sdk_go_v2_credentials",
    "com_github_aws_aws_sdk_go_v2_service_secretsmanager",
    "com_github_bufbuild_protovalidate_go",
    "com_github_bytedance_sonic",
    "com_github_cenkalti_backoff_v4",
    "com_github_datadog_datadog_api_client_go_v2",
    "com_github_datadog_dd_trace_go_v2",
    "com_github_envoyproxy_go_control_plane_envoy",
    "com_github_go_playground_form_v4",
    "com_github_gogo_protobuf",
    "com_github_golang_jwt_jwt_v4",
    "com_github_google_uuid",
    "com_github_gorilla_mux",
    "com_github_gorilla_websocket",
    "com_github_growthbook_growthbook_golang",
    "com_github_ibm_sarama",
    "com_github_lib_pq",
    "com_github_mitchellh_mapstructure",
    "com_github_moegolibrary_moego_api_definitions",
    "com_github_nacos_group_nacos_sdk_go_v2",
    "com_github_openai_openai_go",
    "com_github_opensearch_project_opensearch_go",
    "com_github_opensearch_project_opensearch_go_v4",
    "com_github_redis_go_redis_v9",
    "com_github_robfig_cron_v3",
    "com_github_samber_lo",
    "com_github_shenzhencenter_google_ads_pb",
    "com_github_shopspring_decimal",
    "com_github_slack_go_slack",
    "com_github_smartystreets_goconvey",
    "com_github_spf13_cast",
    "com_github_spf13_cobra",
    "com_github_spf13_pflag",
    "com_github_spf13_viper",
    "com_github_stretchr_testify",
    "com_github_thoas_go_funk",
    "com_github_xuri_excelize_v2",
    "in_gopkg_yaml_v3",
    "io_gorm_datatypes",
    "io_gorm_driver_mysql",
    "io_gorm_driver_postgres",
    "io_gorm_gen",
    "io_gorm_gorm",
    "io_gorm_plugin_dbresolver",
    "io_k8s_api",
    "io_k8s_apimachinery",
    "io_k8s_client_go",
    "io_k8s_sigs_kustomize_kyaml",
    "io_k8s_sigs_yaml",
    "io_opentelemetry_go_otel",
    "io_opentelemetry_go_otel_trace",
    "org_golang_google_api",
    "org_golang_google_genproto",
    "org_golang_google_genproto_googleapis_api",
    "org_golang_google_genproto_googleapis_rpc",
    "org_golang_google_grpc",
    "org_golang_google_protobuf",
    "org_golang_x_net",
    "org_golang_x_oauth2",
    "org_golang_x_sync",
    "org_golang_x_time",
    "org_uber_go_automaxprocs",
    "org_uber_go_mock",
    "org_uber_go_zap",
    "tools_gotest_v3",
)
