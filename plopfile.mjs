/*
 * @since 2023-04-06 15:38:47
 * <AUTHOR> <<EMAIL>>
 */

import inquirerAutocompletePrompt from 'inquirer-autocomplete-prompt';
import * as G from 'glob';
import * as child_process from 'child_process';
import dayjs from 'dayjs';

/**
 * @param {import('plop').NodePlopAPI} plop
 */
export default function (plop) {
  const domains = G.sync('api/moego/service/*/*/', { nodir: false }).map((a) => a.split('/').slice(-2).join('.'));
  plop.setPrompt('autocomplete', inquirerAutocompletePrompt);
  plop.setPartial('user', child_process.execSync('git config user.name', { encoding: 'utf-8' }).trim());
  plop.setPartial('email', child_process.execSync('git config user.email', { encoding: 'utf-8' }).trim());
  plop.setPartial('date', dayjs().format('YYYY-MM-DD HH:mm:ss'));
  plop.setHelper(
    'goPackage',
    (context, arg1) => console.log(context, arg1) || context.replace(/((\.v\d)|_)/g, '') + arg1,
  );
  plop.setHelper('domainPath', (context) => context.replace(/\./g, '/'));
  plop.setGenerator('service', {
    description: 'Create a service skeleton',
    prompts: [
      {
        type: 'autocomplete',
        name: 'domain',
        message: 'Which domain do you want to use?',
        suggestOnly: false,
        async source(args, input) {
          return input
            ? domains.filter((v) => v.startsWith(input)).concat(/\.v\d+$/.test(input) ? input : input + '.v1')
            : domains;
        },
        validate(input, answers) {
          const value = typeof input === 'string' ? input : input.value;
          return /^[a-z][a-z0-9_]*[a-z]\.v\d$/.test(value) || 'Invalid domain name (xxx_yyy.v<n>).';
        },
      },
      {
        type: 'input',
        name: 'service',
        message: "What's your service name? eg: Account",
        validate(input, answers) {
          return /^[A-Z][a-z0-9A-Z]+$/.test(input) || 'Invalid service name (XxxYyy)';
        },
      },
      {
        type: 'checkbox',
        name: 'scopes',
        message: 'What modules are included?',
        default: ['defs', 'enums', 'models', 'service', 'api', 'admin', 'client'],
        choices: ['defs', 'enums', 'models', 'service', 'api', 'admin', 'client'],
      },
    ],
    actions: (args) =>
      args.scopes.map((v) => ({
        type: 'add',
        path: `api/moego/${
          ['defs', 'enums', 'models'].includes(v) ? 'models' : v
        }/{{ domainPath domain }}/{{ snakeCase service }}_${['api', 'admin', 'client'].includes(v) ? 'api' : v}.proto`,
        templateFile: `templates/service/${v}.proto.hbs`,
      })),
  });
}
