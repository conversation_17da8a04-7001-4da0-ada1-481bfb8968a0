# 使用 https://github.com/DanielLiu1123/gencoder 解析数据库表结构生成模版代码，比如 model 等等。
# 1. 安装：go install github.com/DanielLiu1123/gencoder/cmd/gencoder@main
# 2. 配置：修改 database[].tables[] 为需要生成的表名
# 2. 生成：gencoder generate

templates: templates/gencoder
importHelpers:
  - templates/gencoder/helper.js
databases:
  - dsn: 'postgres://moego_developer_240310_eff7a0dc:<EMAIL>:40132/moego_online_booking'
    properties:
      protoModelsPackage: 'moego.models.online_booking.v1'
      protoServicePackage: 'moego.service.online_booking.v1'
      javaModelsPackage: 'com.moego.idl.models.online_booking.v1'
      javaServicePackage: 'com.moego.idl.service.online_booking.v1'
      goModelsPackage: 'github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1;onlinebookingpb'
      goServicePackage: 'github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/online_booking/v1;onlinebookingsvcpb'
    tables:
      - name: 'boarding_service_waitlist'
      - name: 'daycare_service_waitlist'
