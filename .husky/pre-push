#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

npx --no -- git-branch-is -r "^(feature|bugfix|hotfix|gatefix|master|staging-).*$"

# Check if current branch is behind main
currentBranch=$(git branch --show-current)
if [ "$currentBranch" != "main" ]; then
    git fetch origin main:main
    behindCount=$(git rev-list --count HEAD..main)
    if [ "$behindCount" -gt 0 ]; then
        echo "ERROR: Your branch is $behindCount commit(s) behind main."
        echo "Please merge or rebase with main before committing:"
        echo "                  "
        echo "  git merge main  "
        echo "                  "
        exit 1
    else
        echo "branch '${currentBranch}' is up to date with main."
    fi
fi
